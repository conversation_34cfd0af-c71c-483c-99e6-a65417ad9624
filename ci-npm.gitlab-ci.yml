include:
  - local: ci-base.gitlab-ci.yml
  - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/npm-info@feat/gitlabRegistry
  - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/npm-build@feat/gitlabRegistry
    inputs:
      build-number: $BUILD_NUMBER
      build-name: $BUILD_NAME
  # - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/npm-test@feat/gitlabRegistry
  # - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/npm-code-analysis@main
  #   rules:
  #     - if: $SKIP_CODE_QUALITY != "true"
  # - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/npm-code-coverage-report@main
  #   rules:
  #     - if: $SKIP_CODE_QUALITY != "true"

    