include:
  - local: ci-npm.gitlab-ci.yml
  - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/normalize-image@feat/gitlabRegistry
    inputs:
      image-tag: $NPM_VERSION
  - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/docker@feat/gitlabRegistry 
    inputs:
      image_name: $DOCKER_IMAGE
      image_tag: $DOCKER_TAG
  - component: gitlab.dachser.com/dachser/business-integration/dachser-platform/devops/gitlab-components/publish-charts@feat/gitlabRegistry
    inputs:
      application-version: $NPM_VERSION  