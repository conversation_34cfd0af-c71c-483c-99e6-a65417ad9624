spec:
  inputs:
    stage:
      default: build-images
    image:
      default: 'docker-redhat.docker-artifactory.dach041.dachser.com/ubi9/buildah:9.4'
    image_group:
      default: dfe
    image_tag:
      default: latest
    image_name:
      default: $CI_PROJECT_NAME
    stage-publish-docker-info:
      default: .post
    image_publish_docker_info:
      default: 'registry.gitlab.dachser.com/dachser/shared-and-technology-services/development-products/development-platforms/gitlab/gitlab-runner-images/ssh:latest'
    image-upload-to-jfrog-artifactory-enabled:
      type: boolean
      default: true
---
include:
  - remote: "https://releases.jfrog.io/artifactory/jfrog-cli/gitlab/v2/.setup-jfrog-unix.yml"

build-image:
  stage: $[[ inputs.stage ]]
  rules:
    - if: $CI_COMMIT_TAG
  image: $[[ inputs.image ]]
  variables:
    STORAGE_DRIVER: vfs
    JF_REGISTRY: bint-docker-dfe.docker-artifactory.dach041.dachser.com
    ARTIFACTORY_FQ_IMAGE_NAME: $[[ inputs.image_name ]]:$[[ inputs.image_tag ]]
    GITLAB_REGISTRY_FQ_IMAGE_NAME: $CI_REGISTRY_IMAGE:$[[ inputs.image_tag ]]
    IMAGE_UPLOAD_TO_JFROG_ARTIFACTORY_ENABLED: $[[ inputs.image-upload-to-jfrog-artifactory-enabled ]]
    KUBERNETES_EPHEMERAL_STORAGE_LIMIT: 8Gi
  before_script:
    - | 
      if [ $IMAGE_UPLOAD_TO_JFROG_ARTIFACTORY_ENABLED == 'true' ]; then
        # Login to JFrog Artifactory
        buildah login --tls-verify=false  -u "$ARTIFACTORY_USER" --password $ARTIFACTORY_ACCESS_TOKEN ${IMAGE_TARGET_REPO}.${BASE_DOCKER_REPO}
      fi
    # Login to GitLab registry
    - buildah login -u "$CI_REGISTRY_USER" -p "$CI_REGISTRY_PASSWORD" "$CI_REGISTRY"
  script:
    - !reference [.prepare-dachser-certificates, script]
    - buildah build --tls-verify=false -t $IMAGE_NAME:$DOCKER_TAG
    - buildah tag $IMAGE_NAME:$DOCKER_TAG $ARTIFACTORY_FQ_IMAGE_NAME
    - buildah tag $IMAGE_NAME:$DOCKER_TAG $GITLAB_REGISTRY_FQ_IMAGE_NAME
    - | 
      if [ $IMAGE_UPLOAD_TO_JFROG_ARTIFACTORY_ENABLED == 'true' ]; then
        # Pushing image to JFrog Artifactory
        buildah push --tls-verify=false $ARTIFACTORY_FQ_IMAGE_NAME docker://$ARTIFACTORY_FQ_IMAGE_NAME
      fi
    # Pushing image to GitLab registry
    - buildah push $GITLAB_REGISTRY_FQ_IMAGE_NAME
    # Creating metadata for image
    - buildah rmi $ARTIFACTORY_FQ_IMAGE_NAME
    - buildah rmi $GITLAB_REGISTRY_FQ_IMAGE_NAME
    - buildah manifest create local-manifest-list
    - | 
      if [ $IMAGE_UPLOAD_TO_JFROG_ARTIFACTORY_ENABLED == 'true' ]; then
        buildah manifest add --tls-verify=false local-manifest-list docker://$ARTIFACTORY_FQ_IMAGE_NAME
      else
        buildah manifest add --tls-verify=false local-manifest-list docker://$GITLAB_REGISTRY_FQ_IMAGE_NAME
      fi
    - buildah manifest inspect local-manifest-list
    - IMAGE_DIGEST=$(buildah manifest inspect local-manifest-list | grep -i '"digest"' | awk -F'"' '{print $4}')
    - echo $IMAGE_DIGEST
    - echo "$ARTIFACTORY_FQ_IMAGE_NAME@${IMAGE_DIGEST}" >> image-file-details
    - echo "IMAGE_DIGEST=${IMAGE_DIGEST}" > info.env
  artifacts:
    paths:
      - image-file-details
    reports:
      dotenv: info.env
    expire_in: 1 hour

publish-docker-build-info:
  stage: $[[ inputs.stage-publish-docker-info ]]
  image: $[[ inputs.image_publish_docker_info ]]
  variables:
    IMAGE_UPLOAD_TO_JFROG_ARTIFACTORY_ENABLED: $[[ inputs.image-upload-to-jfrog-artifactory-enabled ]]
  rules:
    - if: $CI_COMMIT_TAG && $IMAGE_UPLOAD_TO_JFROG_ARTIFACTORY_ENABLED == 'true'
  script:
    - !reference [.setup_jfrog, script]
    - jf rt build-docker-create "${IMAGE_TARGET_REPO}" --image-file=image-file-details --build-name=$BUILD_NAME --build-number=$BUILD_NUMBER
    - jf rt build-publish $BUILD_NAME $BUILD_NUMBER --project=bint

.prepare-dachser-certificates:
  script:
    - mkdir -p certificates
    - mv $DACH_CERT_CA_BUNDLE certificates/ca-bundle.crt
    - mv $DACH_CERT_CERT_1 certificates/cert-1.crt
    - mv $DACH_CERT_CERTNEW certificates/certnew.cer
    - mv $DACH_CERT_PROXYCERT certificates/proxycert.cer
    - mv $DACH_CERT_ROOT_CERT certificates/root-cert.cer
