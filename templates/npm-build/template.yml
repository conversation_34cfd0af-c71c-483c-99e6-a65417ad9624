spec:
  inputs:
    build-name:
    build-number:
    build-stage:
      default: build
    node-image-name:
      default: registry.gitlab.dachser.com/dachser/shared-and-technology-services/development-products/development-platforms/gitlab/gitlab-runner-images/nodejs:22-latest
    jfrog-project:
      default: bint
    stage-publish-build-info:
      default: post-test
    enable-gitlab-publish:          
      default: "true"               
---
include:
  - local: "/templates/.jfrog.gitlab-ci.yml"

npm-build:
  variables:
    KUBERNETES_MEMORY_LIMIT: 4Gi
    KUBERNETES_EPHEMERAL_STORAGE_LIMIT: 8Gi
  stage: $[[ inputs.build-stage ]]
  extends: [.jf_npm]
  rules:
    - if: $CI_COMMIT_BRANCH || $CI_PIPELINE_SOURCE == "merge_request_event"
  image: $[[ inputs.node-image-name ]]
  script:
    - |
      if [ ! -d "node_modules" ]; then
        jf npm ci --build-name="${BUILD_NAME}" --build-number="${BUILD_NUMBER}" ;
      fi
    - jf npm run build --build-name=$[[ inputs.build-name ]] --build-number=$[[ inputs.build-number ]]


npm-build-publish:
  stage: $[[ inputs.build-stage ]]
  image: $[[ inputs.node-image-name ]]
  extends: [.jf_npm]
  variables:
    KUBERNETES_MEMORY_LIMIT: 4Gi
    KUBERNETES_EPHEMERAL_STORAGE_LIMIT: 5Gi
  rules:
    - if: $CI_COMMIT_TAG
  script:
    - |
      if [ ! -d "node_modules" ]; then
        jf npm ci --build-name="${BUILD_NAME}" --build-number="${BUILD_NUMBER}" ;
      fi
    - jf npm run build --build-name=$[[ inputs.build-name ]] --build-number=$[[ inputs.build-number ]]
    - jf npm publish --build-name=$[[ inputs.build-name ]] --build-number=$[[ inputs.build-number ]] --project=$[[ inputs.jfrog-project ]]
    # Also publish to GitLab Package Registry (parallel to Artifactory)
    - |
      if [ "$[[ inputs.enable-gitlab-publish ]]" = "true" ]; then
        echo "Publishing to GitLab Package Registry..."
        cp .npmrc .npmrc.backup || true
        echo "@dfe:registry=https://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/" >> .npmrc
        echo "//${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/:_authToken=${CI_JOB_TOKEN}" >> .npmrc
        npm publish --registry=https://${CI_SERVER_HOST}/api/v4/projects/${CI_PROJECT_ID}/packages/npm/ || echo "GitLab publish failed, continuing..."
        mv .npmrc.backup .npmrc || true
      else
        echo "GitLab publishing disabled"
      fi

npm-publish-jfrog-build-info:
  stage: $[[ inputs.stage-publish-build-info ]]
  image: $[[ inputs.node-image-name ]]
  extends: [.jf_npm]
  variables:
    BUILD_NAME: $[[ inputs.build-name ]]
    BUILD_NUMBER: $[[ inputs.build-number ]]
  rules:
    - if: $CI_COMMIT_TAG
  script:
    - !reference [.setup_jfrog, script]
    - jf rt build-publish $BUILD_NAME $BUILD_NUMBER --project=$[[ inputs.jfrog-project ]]

npm-publish-gitlab-build-info:
  stage: $[[ inputs.stage-publish-build-info ]]
  image: $[[ inputs.node-image-name ]]
  variables:
    BUILD_NAME: $[[ inputs.build-name ]]
    BUILD_NUMBER: $[[ inputs.build-number ]]
    ENABLE_GITLAB_PUBLISH: $[[ inputs.enable-gitlab-publish ]]
  rules:
    - if: $CI_COMMIT_TAG && $ENABLE_GITLAB_PUBLISH == "true"
  script:
    - echo "Publishing npm build info to GitLab registry"
    - echo "Build Name: $BUILD_NAME"
    - echo "Build Number: $BUILD_NUMBER"
    - echo "Package: $CI_PROJECT_NAME"
    - echo "Registry: $CI_API_V4_URL/projects/$CI_PROJECT_ID/packages/npm"

