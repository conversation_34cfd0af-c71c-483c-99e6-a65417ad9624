spec:
  inputs:
    build-stage:
      default: build
    helm-image-name:
      default: registry.gitlab.dachser.com/dachser/shared-and-technology-services/development-products/development-platforms/gitlab/gitlab-runner-images/helm:latest
---
include:
  - local: "/templates/.jfrog.gitlab-ci.yml"

default:
  tags:
    - openshift

publish-helm-chart:
  stage: $[[ inputs.build-stage ]]
  image: $[[ inputs.helm-image-name ]]
  extends: [.jf_java]
  variables:
    JF_HELM_REGISTRY: bint-helm-dfe
  script:
    - "CHART_VERSION=$(helm show chart . | grep 'version:*' | sed -r 's/^version: //')"
    - echo $CHART_VERSION
    - "APP_VERSION=$(helm show chart . | grep 'appVersion:*' | sed -r 's/^appVersion: //')"
    - echo $APP_VERSION
    - echo "Package helm chart"
    - helm package . --version $CHART_VERSION --app-version $APP_VERSION
    - echo "Deploy helm chart"
    - jf rt u "*.tgz" $JF_HELM_REGISTRY --project=bint 
  only:
    - master
