.java-truststore:
  script:
    - |
      mkdir -p /truststore
      echo "$ADDITIONAL_CA_CERT_BUNDLE" > ca-bundle.pem
      awk 'BEGIN {c=0;} /BEGIN CERT/{c++} { print > "/truststore/custom-ca-" c ".pem"}' < ca-bundle.pem
      for file in /truststore/*.pem; do
      keytool -import -noprompt -keystore $JAVA_TRUSTSTORE -file $file -storepass changeit -alias $(basename $file)
      rm $file
      done
