spec:
  inputs:
    stage:
      default: .post
    image:
      default: registry.gitlab.dachser.com/dachser/shared-and-technology-services/development-products/development-platforms/gitlab/gitlab-runner-images/ssh:latest

---
.create-mr:
  stage: $[[ inputs.stage ]]
  image: $[[ inputs.image ]]
  rules:
    - if: $CI_COMMIT_TAG && $CI_COMMIT_TAG !~ $BETA_PATTERN && $CI_COMMIT_TAG !~ $ALPHA_PATTERN
  variables:
    SOURCE_BRANCH: $MASTER_BRANCH
    TARGET_BRANCH: $BETA_BRANCH
    TITLE: "Release $CI_COMMIT_TAG"
    DESCRIPTION: "Update new newest version of $SOURCE_BRANCH into $TARGET_BRANCH"
  script:
    - echo "Creating a Merge Request..."
    - |
      curl --request POST "${CI_API_V4_URL}/projects/${CI_PROJECT_ID}/merge_requests" \
        --header "PRIVATE-TOKEN: ${GITLAB_TOKEN}" \
        --header "Content-Type: application/json" \
        --data "{
          \"source_branch\": \"${SOURCE_BRANCH}\",
          \"target_branch\": \"$TARGET_BRANCH\",
          \"title\": \"${TITLE}\",
          \"description\": \"${DESCRIPTION}\",
          \"remove_source_branch\": true
        }"

mr-master-into-beta:
  extends: .create-mr
  variables:
    SOURCE_BRANCH: $MASTER_BRANCH
    TARGET_BRANCH: $BETA_BRANCH

mr-master-into-develop:
  extends: .create-mr
  variables:
    SOURCE_BRANCH: $MASTER_BRANCH
    TARGET_BRANCH: $ALPHA_BRANCH
